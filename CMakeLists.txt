cmake_minimum_required(VERSION 3.16)

project(qlcplus VERSION 4.14.3 LANGUAGES CXX)

# Find Qt first to determine version
find_package(Qt6 QUIET COMPONENTS Core Gui Widgets)
if(Qt6_FOUND)
    set(QT_VERSION_MAJOR 6)
else()
    find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets)
    set(QT_VERSION_MAJOR 5)
endif()

# Include common variables and settings
include(${CMAKE_CURRENT_SOURCE_DIR}/variables.cmake)

# Find required packages
find_package(PkgConfig REQUIRED)

# Add subdirectories
add_subdirectory(hotplugmonitor)
add_subdirectory(engine)
add_subdirectory(plugins)
add_subdirectory(resources)

if(NOT qmlui)
    add_subdirectory(ui)
    add_subdirectory(webaccess)
    add_subdirectory(main)
    add_subdirectory(fixtureeditor)
    if(APPLE)
        add_subdirectory(launcher)
    endif()
else()
    add_subdirectory(qmlui)
endif()

# Add platforms if needed
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/platforms/CMakeLists.txt")
    add_subdirectory(platforms)
endif()
