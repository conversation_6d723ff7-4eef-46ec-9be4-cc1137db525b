#include <QApplication>
#include <QTimer>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QStandardPaths>
#include "engine/src/doc.h"
#include "engine/src/autosavemanager.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Create a test document
    Doc* doc = new Doc(nullptr);
    
    // Create autosave manager
    AutoSaveManager* autosaveManager = new AutoSaveManager(doc);
    
    // Enable autosave with 5 second interval for testing
    autosaveManager->setEnabled(true);
    autosaveManager->setInterval(5000); // 5 seconds
    
    qDebug() << "AutoSave enabled:" << autosaveManager->isEnabled();
    qDebug() << "AutoSave interval:" << autosaveManager->interval() << "ms";
    qDebug() << "AutoSave directory:" << autosaveManager->autosaveDirectory();
    
    // Simulate some changes to the document
    QTimer::singleShot(2000, [doc]() {
        qDebug() << "Making changes to document...";
        doc->setModified();
    });
    
    // Check for autosave files after 10 seconds
    QTimer::singleShot(10000, [autosaveManager, &app]() {
        QDir autosaveDir(autosaveManager->autosaveDirectory());
        QStringList autosaveFiles = autosaveDir.entryList(QStringList() << "*.qxw.autosave", QDir::Files);
        
        qDebug() << "Autosave files found:" << autosaveFiles.size();
        for (const QString& file : autosaveFiles) {
            qDebug() << "  -" << file;
        }
        
        app.quit();
    });
    
    return app.exec();
}
